# Development dependencies for HyDocPusher
# Version: 1.0.0
# Python Version: 3.9.6

# Testing Framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-cov==4.1.0
pytest-xdist==3.5.0
pytest-httpx==0.27.0
pytest-redis==3.0.2

# Code Quality
black==23.11.0
flake8==6.1.0
isort==5.12.0
autopep8==2.0.4
mypy==1.7.1

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
mkdocs==1.5.3
mkdocs-material==9.5.3

# Development Tools
ipython==8.18.1
jupyter==1.0.0
notebook==7.0.6

# Debugging
pdb++==0.10.4
ipdb==0.13.13

# Performance Testing
locust==2.17.0
pytest-benchmark==4.0.0

# Security
bandit==1.7.5
safety==2.3.5

# Build Tools
build==1.0.3
twine==4.0.2
wheel==0.41.3

# Docker Integration
docker==6.1.3

# Git Hooks
pre-commit==3.5.0

# Monitoring (Development)
watchdog==3.0.0
psutil==5.9.6