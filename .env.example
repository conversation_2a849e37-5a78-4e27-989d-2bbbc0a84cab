# HyDocPusher 环境配置示例文件
# 复制此文件为 .env 并根据实际环境修改配置值

# =============================================================================
# Pulsar 配置
# =============================================================================

# Pulsar 集群地址 (注意：这里使用的是HTTP管理端口，实际连接需要使用Pulsar协议端口)
# 开发环境示例：pulsar://**************:6650
PULSAR_CLUSTER_URL=pulsar://**************:6650

# Pulsar 租户和命名空间
PULSAR_TENANT=bigdata
PULSAR_NAMESPACE=text

# Topic 配置 (只需要指定topic名称，完整路径会自动构建)
PULSAR_TOPIC=user-to-pretreat
PULSAR_SUBSCRIPTION=hydocpusher-subscription
PULSAR_DEAD_LETTER_TOPIC=hydocpusher-dlq

# Pulsar 连接超时配置 (毫秒)
PULSAR_CONNECTION_TIMEOUT=30000
PULSAR_OPERATION_TIMEOUT=30000

# =============================================================================
# 档案系统配置
# =============================================================================

# 档案系统 API 地址
ARCHIVE_API_URL=http://***********:8080/news/archive/receive

# 档案系统认证信息
ARCHIVE_APP_ID=NEWS
ARCHIVE_APP_TOKEN=TmV3cytJbnRlcmZhY2U=

# 档案系统业务配置
ARCHIVE_COMPANY_NAME=云南省能源投资集团有限公司
ARCHIVE_ARCHIVE_TYPE=17
ARCHIVE_DOMAIN=www.cnyeig.com
ARCHIVE_RETENTION_PERIOD=30

# 档案系统请求配置 (毫秒)
ARCHIVE_TIMEOUT=30000
ARCHIVE_RETRY_MAX_ATTEMPTS=3
ARCHIVE_RETRY_DELAY=60000

# =============================================================================
# 应用配置
# =============================================================================

# 服务配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# 应用信息
APP_NAME=HyDocPusher
APP_VERSION=1.0.0
DEBUG=false

# 性能配置
MAX_CONCURRENT_MESSAGES=100
MESSAGE_PROCESSING_TIMEOUT=300000

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOGGING_LEVEL=INFO
LOGGING_FORMAT=%(asctime)s [%(levelname)s] %(name)s: %(message)s

# 日志文件配置 (可选)
LOGGING_FILE_PATH=logs/hydocpusher.log
LOGGING_MAX_FILE_SIZE=10MB
LOGGING_BACKUP_COUNT=5

# =============================================================================
# 分类映射配置
# =============================================================================

# 分类规则文件路径
CLASSIFICATION_RULES_FILE=config/classification-rules.yaml
CLASSIFICATION_DEFAULT_CLASSFYNAME=其他
CLASSIFICATION_DEFAULT_CLASSFY=QT