[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "hydocpusher"
version = "1.0.0"
description = "内容发布数据归档同步服务"
readme = "README.md"
requires-python = ">=3.9.6,<3.10.0"
license = {text = "MIT"}
authors = [
    {name = "Development Team", email = "<EMAIL>"},
]
maintainers = [
    {name = "Development Team", email = "<EMAIL>"},
]
keywords = ["pulsar", "archive", "sync", "data-processing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.9.6",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Archiving",
    "Topic :: System :: Distributed Computing",
]
dependencies = [
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",
    "pydantic==2.5.0",
    "pydantic-settings==2.1.0",
    "httpx==0.25.2",
    "aiohttp==3.9.1",
    "pulsar-client==3.3.0",
    "python-dotenv==1.0.0",
    "PyYAML==6.0.1",
    "structlog==23.2.0",
    "prometheus-client==0.19.0",
    "asyncio-mqtt==0.16.1",
    "aiofiles==23.2.1",
    "orjson==3.9.10",
    "python-dateutil==2.8.2",
    "pytz==2023.3",
    "cryptography==41.0.7",
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",
    "click==8.1.7",
    "rich==13.7.0",
    "typing-extensions==4.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest==7.4.3",
    "pytest-asyncio==0.21.1",
    "pytest-mock==3.12.0",
    "pytest-cov==4.1.0",
    "pytest-xdist==3.5.0",
    "pytest-httpx==0.27.0",
    "black==23.11.0",
    "flake8==6.1.0",
    "isort==5.12.0",
    "mypy==1.7.1",
    "bandit==1.7.5",
    "safety==2.3.5",
    "pre-commit==3.5.0",
]
test = [
    "pytest==7.4.3",
    "pytest-asyncio==0.21.1",
    "pytest-mock==3.12.0",
    "pytest-cov==4.1.0",
    "pytest-httpx==0.27.0",
]
docs = [
    "sphinx==7.2.6",
    "sphinx-rtd-theme==1.3.0",
    "mkdocs==1.5.3",
    "mkdocs-material==9.5.3",
]

[project.urls]
Homepage = "https://github.com/example/hydocpusher"
Documentation = "https://hydocpusher.readthedocs.io"
Repository = "https://github.com/example/hydocpusher.git"
"Bug Tracker" = "https://github.com/example/hydocpusher/issues"

[project.scripts]
hydocpusher = "hydocpusher.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["hydocpusher*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
hydocpusher = ["config/*.yaml", "config/*.yml"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["hydocpusher"]

[tool.mypy]
python_version = "3.9.6"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pulsar.*",
    "prometheus_client.*",
    "structlog.*",
    "yaml.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["hydocpusher"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/conftest.py",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "scripts"]
skips = ["B101", "B601"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".tox",
    ".venv",
]

[tool.bandit.assert_used]
skips = ['*/tests/*', '*/test_*.py']