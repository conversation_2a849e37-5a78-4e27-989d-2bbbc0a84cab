# Docker Compose for HyDocPusher
# Version: 1.0.0

version: '3.8'

services:
  hydocpusher:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hydocpusher
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-dev}
      - PYTHONPATH=/app
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./scripts:/app/scripts:ro
    depends_on:
      - pulsar
      - redis
    networks:
      - hydocpusher-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  pulsar:
    image: apachepulsar/pulsar:2.10.0
    container_name: pulsar
    ports:
      - "6650:6650"
      - "8080:8080"
    environment:
      - PULSAR_STANDALONE_USE_ZOOKEEPER=false
      - PULSAR_PREFIX_hydocpusher-dlq=persistent://public/default/hydocpusher-dlq
    volumes:
      - pulsar-data:/pulsar/data
      - pulsar-logs:/pulsar/logs
    networks:
      - hydocpusher-network
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - hydocpusher-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - hydocpusher-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - hydocpusher-network
    restart: unless-stopped

  # Mock archive service for testing
  mock-archive:
    image: mockserver/mockserver:latest
    container_name: mock-archive
    ports:
      - "8081:1080"
    environment:
      - MOCKSERVER_PROPERTY_FILE=/config/mockserver.properties
    volumes:
      - ./docker/mockserver:/config:ro
    networks:
      - hydocpusher-network
    restart: unless-stopped

volumes:
  pulsar-data:
  pulsar-logs:
  redis-data:
  prometheus-data:
  grafana-data:

networks:
  hydocpusher-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16