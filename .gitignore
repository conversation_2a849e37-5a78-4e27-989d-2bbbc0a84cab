# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
logs/
*.log
config/*.yaml
config/*.yml
!config/*.yaml.example
!config/*.yml.example
!config/classification-rules.yaml

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
.tmp/

# Backup files
*.bak
*.backup

# Docker
.dockerignore

# Kubernetes
.k8s/
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# AWS
.aws/

# Secrets and sensitive files
secrets/
*.key
*.pem
*.crt
*.p12
*.pfx
.env
.env.local
.env.production
.env.staging

# Test data
test_data/
sample_data/

# Coverage reports
htmlcov/
coverage.xml
*.cover
.hypothesis/

# Documentation build
docs/build/
docs/_build/

# Node.js (if any frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.local/

# Cache
.cache/
.pytest_cache/
.mypy_cache/

# Virtual environment wrapper
.env.venv

# Poetry
poetry.lock

# PDM
.pdm.toml

# Hatch
.hatch/

# Ruff
.ruff_cache/

# Bandit
.bandit/

# Safety
.safety/

# Pre-commit
.pre-commit-config.yaml

# Docker volumes
docker-volumes/

# Kubernetes secrets
secrets/

# HyDocPusher specific
# Runtime logs
logs/
*.log

# Message processing temp files
temp_messages/
message_cache/

# Archive processing
archive_temp/
processing/

# Performance monitoring
metrics/
monitoring/

# Claude init directory (embedded git repo)
claude-init/