你是一位经验丰富的软件架构师和技术负责人，精通测试驱动开发（TDD）和敏捷开发方法论。你的核心任务是根据我提供的【项目名称】的需求文档和技术规范，创建一个详细、可执行的TDD开发计划。

这个计划必须将整个项目拆解成一系列独立的、可测试的、可分阶段编码的功能任务（TASK）。每个任务都应包含明确的功能描述、一个清晰的编码指令（prompt），以及一组用于验证功能的TDD测试用例。

#### 项目背景 (Project Context)

  * **项目名称:** 【例如：在线课程平台的后台管理系统】
  * **核心目标:** 【例如：构建一个稳定、高效的后台系统，用于管理课程、学生和教师信息】
  * **技术栈/语言:** 【例如：Python, Django Rest Framework, PostgreSQL, Docker】
  * **关键特性列表 (简述):**
      * 【特性1：用户认证与授权模块】
      * 【特性2：课程管理模块（CRUD操作）】
      * 【特性3：学生信息管理模块】
      * 【特性4：订单与支付集成模块】
      * 【...更多特性】

#### 任务拆解和输出格式要求 (Task Breakdown and Output Format)

请严格按照以下格式为每一个功能点生成一个任务（TASK）：

```markdown
**TASKXXX: [功能模块/特性名称]**

* **功能描述 (Functional Description):**
    * [对该功能点的详细、清晰的文字描述。说明它的作用、输入和预期输出。]

* **前置任务 (Prerequisites):**
    * [完成此任务前需要先完成的其他TASK编号，例如：TASK001。如果没有，则填写“无”。]

* **编码提示词 (Coding Prompt for Developer/AI):**
    * [这里是给后续开发人员或另一个AI编码工具的具体指令。需要清晰、无歧义。例如：“请使用Python和Django Rest Framework，创建一个名为`CourseViewSet`的视图集，它需要继承自`ModelViewSet`，并实现对`Course`模型的完整CRUD（创建、读取、更新、删除）操作。确保API遵循RESTful设计原则，并为未授权的访问返回403 Forbidden状态码。”]

* **TDD测试用例 (TDD Test Cases):**
    * [必须先写测试用例！这些用例应覆盖成功、失败和边界情况。格式：**用例名称 - 描述**]
    * **Test Case 1: [测试用例名称，例如：成功获取课程列表]**
        * **Given:** 数据库中有3个已发布的课程，且用户已通过身份验证。
        * **When:** 用户向`/api/courses/`端点发送一个GET请求。
        * **Then:** 系统应返回200 OK状态码，响应体中应包含一个包含3个课程对象的JSON数组。
    * **Test Case 2: [测试用例名称，例如：未经授权的用户尝试获取课程列表]**
        * **Given:** 一个未经身份验证的匿名用户。
        * **When:** 该用户向`/api/courses/`端点发送一个GET请求。
        * **Then:** 系统应返回403 Forbidden或401 Unauthorized状态码。
    * **Test Case 3: [测试用例名称，例如：成功创建一个新课程]**
        * **Given:** 一个具有管理员权限的已认证用户。
        * **When:** 用户向`/api/courses/`端点发送一个包含有效课程数据（如：标题、描述、价格）的POST请求。
        * **Then:** 系统应返回201 Created状态码，数据库中应成功创建一个新的课程记录，并且响应体中包含新创建的课程信息。
    * **Test Case 4: [测试用例名称，例如：创建课程时缺少必要字段]**
        * **Given:** 一个具有管理员权限的已认证用户。
        * **When:** 用户发送的POST请求数据中缺少“标题”（title）字段。
        * **Then:** 系统应返回400 Bad Request状态码，并附带错误信息指出“标题字段是必需的”。
    * **[...根据需要添加更多测试用例，覆盖更新、删除、边界值等场景。]**
```

#### 需求文档和技术规范

[-- 请在此处粘贴您的详细需求文档、功能设计和技术规范定义 --]

**--- 模板结束 ---**

### 如何使用这个模板

1.  **填充信息:** 将上面模板中所有带 `【】` 的部分替换成您自己的项目信息。在最后的 `[-- ... --]` 部分，粘贴您已经准备好的需求和设计文档。您的文档越详细，AI生成的开发计划就越准确。
2.  **提交给AI:** 将填充好的完整提示词复制并粘贴到您选择的AI编码工具中。
3.  **审查和迭代:** AI会根据您的输入生成一系列的`TASK`。您需要仔细审查这些任务：
      * 拆解得是否合理？有没有遗漏的功能点？
      * `功能描述`是否准确？
      * `编码提示词`是否清晰，足以让开发者或另一个AI直接开始工作？
      * `TDD测试用例`是否全面？是否真的遵循了TDD的原则（即通过测试来驱动功能实现）？
4.  **分阶段开发:** 当您对计划满意后，就可以逐个`TASK`地进行开发了。您可以将每个`TASK`中的`编码提示词`和`TDD测试用例`再次交给AI编码工具，让它：
      * 首先，根据`TDD测试用例`编写测试代码（此时运行测试应该是失败的 - **Red**）。
      * 然后，让它根据`编码提示词`编写最少的代码来通过这些测试（**Green**）。
      * 最后，可以再给一个指令进行代码优化和重构（**Refactor**）。

通过这种方式，您就建立了一个高效、结构化且质量可控的AI辅助开发流程。