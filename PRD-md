### **PRD：内容发布数据归档同步服务**

#### **1. 背景与目标**

**1.1. 背景**
当前，业务系统在内容发布成功后，需要按照档案管理规范，将发布的内容元数据及原文附件信息归档至第三方电子档案管理系统。为实现此流程的自动化、解耦和高可靠性，避免手动归档或批处理带来的延迟和错漏，我们计划开发一个数据同步服务，实时处理内容发布数据并完成归档。

**1.2. 目标**
开发一个独立的数据服务，实现以下核心目标：

  * **自动化归档**：自动消费内容发布系统的消息，无需人工干预。
  * **数据标准化**：将源系统数据结构转换为符合电子档案管理系统要求的标准结构。
  * **系统解耦**：通过消息队列作为中间件，降低内容发布系统与档案系统的直接耦合。
  * **可靠同步**：确保数据能够被准确、可靠地推送至目标系统，并具备错误记录能力。

#### **2. 功能需求**

**2.1. 核心流程**
服务整体流程分为三个核心步骤：数据消费、数据转换、数据推送。

**2.2. 功能详述**

**FR-1: 消费原始数据**

  * **FR-1.1**: 服务必须能够连接至指定的Pulsar消息队列集群。
  * **FR-1.2**: 服务需订阅指定的Topic，以消费内容发布成功后产生的原始数据消息。
  * **FR-1.3**: 接收到的消息体为JSON格式，服务需要能正确解析该JSON结构。
  * **FR-1.4**: 需具备健壮性，当遇到无法解析的恶意或错误格式消息时，应记录错误日志并跳过该消息，不应导致服务中断。

**FR-2: 数据转换与构建**

  * **FR-2.1**: 服务需内置一个数据转换模块，负责将从Pulsar消费的原始JSON数据，转换为目标API要求的JSON结构。
  * **FR-2.2: 数据转换规则**
    数据转换模块必须严格按照以下规则，将从 Pulsar 消费的原始 JSON 数据映射到目标接口所需的 JSON 结构。

  * **2.2.1. 顶层字段映射**

    * **`AppId`**: 固定值，从配置文件读取。 (默认值: `NEWS`)
    * **`AppToken`**: 固定值，从配置文件读取。 (默认值: `TmV3cytJbnRlcmZhY2U=`)
    * **`CompanyName`**: 固定值，从配置文件读取。 (默认值: `云南省能源投资集团有限公司`)
    * **`ArchiveType`**: 固定值，从配置文件读取。 (默认值: `17`)
    * **`ArchiveData`**: 一个JSON对象，其内部字段映射规则如下。

  * **2.2.2. `ArchiveData` 对象字段映射**

    * **`did`**: 取原始数据中 `DATA.DATA.DOCID` 字段的值。
    * **`wzmc`**: 固定字符串 `集团门户`。
    * **`dn`**: 固定字符串 `www.cnyeig.com`。
    * **`classfyname`**: 通过外部配置文件中的规则进行映射。
    * **`classfy`**: 通过外部配置文件中的规则进行映射。
    * **`title`**: 取原始数据中 `DATA.DATA.DOCTITLE` 字段的值。
    * **`author`**: 取原始数据中 `DATA.DATA.TXY` 字段的值。
    * **`docdate`**: 取原始数据中 `DATA.DATA.DOCFIRSTPUBTIME` 字段的值，并格式化为 `YYYY-MM-DD` 日期格式。
    * **`year`**: 取原始数据中 `DATA.DATA.DOCFIRSTPUBTIME` 字段值的年份部分 (YYYY)。
    * **`retentionperiod`**: 固定值，30。
    * **`fillingdepartment`**: 取原始数据中 `DATA.CHNLDOC.CRDEPT` 字段的值。
    * **`bly`**: 取原始数据中 `DATA.DATA.CRUSER` 字段的值。
    * **`attachment`**: 一个JSON数组，按以下逻辑动态构建：
        * **第一个元素 (正文):**
            * `name`: 取 `DATA.DATA.DOCTITLE` 的值，并拼接字符串 `(正文)`。
            * `ext`: 解析 `DATA.DATA.DOCPUBURL` 字段，获取其文件扩展名 (如 `html`)。
            * `file`: 取 `DATA.DATA.DOCPUBURL` 字段的完整URL值。
            * `type`: 固定字符串 `正文`。
        * **后续元素 (附件):**
            * 遍历原始数据中的 `DATA.APPENDIX` 数组。
            * 对数组中每个对象，创建一个新的附件对象：
                * `name`: 生成一个唯一名称，例如 `附件<序号>_<类型>` (如: `附件1_视频`)。
                * `ext`: 解析当前对象中 `APPFILE` 字段的文件扩展名 (如 `mp4`, `jpg`)。
                * `file`: 将 `DATA.DATA.WEBHTTP` 的值与当前对象中 `APPFILE` 的值拼接，形成一个完整的、可访问的URL。
  * **FR-2.3 (配置化映射)**:
      * `classfyname` (栏目) 和 `classfy` (类别代码) 字段的映射规则必须通过外部配置文件实现。
      * 配置文件应支持基于原始数据中的 `CHANNELID` 或 `CHNLNAME` 等字段，查询并映射出正确的 `classfyname` 和 `classfy` 值。
      * **示例配置 (config.yaml)**:
        ```yaml
        classification_rules:
          - channel_id: "2240"
            classfyname: "新闻头条"
            classfy: "XWTT"
          - channel_id: "2241"
            classfyname: "集团要闻"
            classfy: "JTYW"
          - default:
              classfyname: "其他"
              classfy: "QT"
        ```
  * **FR-2.4 (默认值与固定值)**:
      * 对于 `AppId`, `AppToken`, `CompanyName`, `ArchiveType` 等固定字段，其值应从配置文件中读取。
      * 对于 `retentionperiod` (保管期限), `dn` (网站域名) 等业务配置字段，其值也应从配置文件中读取。
  * **FR-2.5 (动态构建)**:
      * `attachment` 数组需要动态构建。正文信息来自原始数据的 `DOCPUBURL` 字段。
      * 其他附件信息（如图片、视频）需要解析原始数据中的 `APPENDIX` 或 `DOCUMENT_RELATED_PIC` 等数组，并拼接成完整的、可公网访问的URL。

**FR-3: 推送至第三方**

  * **FR-3.1**: 服务需将转换并构建好的最终JSON数据，通过HTTP POST请求，发送至电子档案管理系统的归档接口。
  * **FR-3.2**: 接口地址、超时时间等请求参数需支持在配置文件中设置。
      * [cite\_start]**接口地址**: `http://10.20.162.1:8080/news/archive/receive` [cite: 15]
      * [cite\_start]**调用方式**: `POST` [cite: 17]
  * **FR-3.3**: 服务必须能处理并记录接口的返回结果。
      * [cite\_start]若返回的 `STATUS` 为 `0`，则记录成功日志，包含`DATAID` [cite: 31, 33, 35]。
      * [cite\_start]若 `STATUS` 非 `0`，则必须记录详细的错误日志，包含错误码、错误描述`DESC`以及原始请求报文，以便排查 [cite: 33, 35]。

#### **3. 非功能需求**

  * **NF-1 (配置化)**: 所有外部依赖（Pulsar地址、Topic、API地址、各类默认值、映射规则）必须通过外部配置文件管理，不可硬编码。
  * **NF-2 (日志记录)**: 需具备完善的日志记录能力。
      * **INFO**: 记录服务的启动、停止、成功消费和成功推送的关键节点。
      * **ERROR**: 记录所有异常情况，包括消息消费失败、数据转换失败、API请求失败等，并提供上下文信息（如消息ID）。
  * **NF-3 (可靠性)**:
      * 当API推送失败（如网络超时、目标服务5xx错误）时，应有重试机制（例如：重试3次，间隔1分钟）。
      * 若重试后依然失败，该消息应被投递至死信队列（Dead Letter Queue），以便后续手动补偿。
  * **NF-4 (性能)**: 服务应能支持至少100条/秒的消息处理能力，端到端处理延迟（从消费到推送完成）应在500ms以内。

#### **4. 范围说明**

**4.1. 范围内 (In Scope)**

  * 开发上述完整的数据消费、转换、推送服务。
  * 提供部署所需的Dockerfile及配置文件模板。
  * 提供清晰的日志输出，便于运维。

**4.2. 范围外 (Out of Scope)**

  * Pulsar及电子档案管理系统的搭建与维护。
  * 历史存量数据的回溯归档。
  * 用于管理和监控此服务的可视化界面（UI）。